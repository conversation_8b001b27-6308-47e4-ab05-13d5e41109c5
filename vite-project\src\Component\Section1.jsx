import { useEffect, useRef, useState } from "react";
import { gsap } from "gsap";
import leftArrow from "../assets/leftArrow.png";
import rightArrow from "../assets/rightArrow.png";
import img1 from "../../src/assets/img1.png";
import img2 from "../../src/assets/img2.png";
import img3 from "../../src/assets/img3.png";

export default function Section1() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef(null);
  const progressBarRef = useRef(null);
  const progressFillRef = useRef(null);
  const slideItemsRef = useRef([]);

  const slides = [
    {
      id: 0,
      title: "Kindergarten",
      description:
        "The Kindergarten Program at Canadian Bilingual School focuses on a set of habits that favour the consolidation of individual values, collective values and procedural values.",
      extendedDescription:
        "Our comprehensive program includes the essential modules to take advantage of the foundation acquired in early childhood education and further we incorporate effective practices for the later stages. The curriculum is created totally based on the development.",
      images: [img1, img2, img3],
    },
    {
      id: 1,
      title: "Elementary School",
      description:
        "Elementary School program builds upon the foundation established in Kindergarten, focusing on core academic skills and social development.",
      extendedDescription:
        "Our elementary curriculum emphasizes critical thinking, creativity, and collaboration. Students engage in hands-on learning experiences that prepare them for advanced academic challenges while fostering a love for learning.",
      images: [img1, img2, img3],
    },
    {
      id: 2,
      title: "High School",
      description:
        "High School program prepares students for higher education and future careers through advanced coursework and specialized programs.",
      extendedDescription:
        "Our high school curriculum offers diverse pathways including STEM, humanities, and arts programs. Students develop leadership skills, critical thinking abilities, and prepare for university admission and career success.",
      images: [img1, img2, img3],
    },
  ];

  useEffect(() => {
    // Animate progress bar on mount
    gsap.set(progressFillRef.current, { width: "33.33%" });

    // Initialize slider position
    const slideContainer = sliderRef.current;
    if (slideContainer) {
      gsap.set(slideContainer.querySelector('.slides-wrapper'), { x: 0 });
    }

    // Animate slide items on mount
    gsap.fromTo(
      slideItemsRef.current,
      { opacity: 0, y: 50 },
      { opacity: 1, y: 0, duration: 0.8, stagger: 0.2, ease: "power2.out" }
    );
  }, []);

  const updateProgressBar = (slideIndex) => {
    const progressPercentage = ((slideIndex + 1) / slides.length) * 100;
    gsap.to(progressFillRef.current, {
      width: `${progressPercentage}%`,
      duration: 0.6,
      ease: "power2.out",
    });
  };

  const animateSlideChange = (newSlideIndex) => {
    const slideContainer = sliderRef.current;
    if (!slideContainer) return;

    const slideWidth = slideContainer.offsetWidth;
    const translateX = -newSlideIndex * slideWidth;

    // Animate the slide container to show the new slide with direct scrolling
    gsap.to(slideContainer.querySelector('.slides-wrapper'), {
      x: translateX,
      duration: 0.8,
      ease: "none" // Remove easing for direct scrolling effect
    });
  };

  const handlePrevSlide = () => {
    if (currentSlide > 0) {
      const newSlide = currentSlide - 1;
      setCurrentSlide(newSlide);
      updateProgressBar(newSlide);
      animateSlideChange(newSlide);
    }
  };

  const handleNextSlide = () => {
    if (currentSlide < slides.length - 1) {
      const newSlide = currentSlide + 1;
      setCurrentSlide(newSlide);
      updateProgressBar(newSlide);
      animateSlideChange(newSlide);
    }
  };

  const handleProgressClick = (index) => {
    if (index !== currentSlide) {
      setCurrentSlide(index);
      updateProgressBar(index);
      animateSlideChange(index);
    }
  };

  return (
    <div className="py-20 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="flex justify-between items-start mb-16">
        <div className="w-[70%]">
          <h5 className="text-lg font-semibold tracking-widest text-gray-600 mb-4">
            OUR PROGRAMS
          </h5>
          <h3 className="text-4xl font-bold tracking-tight text-gray-900">
            JOURNEY OF EDUCATION AT CBS
          </h3>
        </div>
        <div className="flex gap-4 pt-10">
          <button
            onClick={handlePrevSlide}
            disabled={currentSlide === 0}
            className={`transition-opacity duration-300 ${
              currentSlide === 0
                ? "opacity-50 cursor-not-allowed"
                : "opacity-100 hover:opacity-80"
            }`}
          >
            <img className="w-12 h-12" src={leftArrow} alt="Previous" />
          </button>
          <button
            onClick={handleNextSlide}
            disabled={currentSlide === slides.length - 1}
            className={`transition-opacity duration-300 ${
              currentSlide === slides.length - 1
                ? "opacity-50 cursor-not-allowed"
                : "opacity-100 hover:opacity-80"
            }`}
          >
            <img className="w-12 h-12" src={rightArrow} alt="Next" />
          </button>
        </div>
      </div>

      {/* Progress Bar Section */}
      <div className="mb-12 w-[70%]">
        <div
          ref={progressBarRef}
          className="relative w-full h-1 bg-gray-200 rounded-full overflow-hidden"
        >
          <div
            ref={progressFillRef}
            className="absolute top-0 left-0 h-full bg-red-500 rounded-full transition-all duration-600 ease-out"
          />

          {/* Progress Points */}
          <div className="absolute top-0 left-0 w-full h-[80%] flex justify-between items-center">
            {slides.map((slide, index) => (
              <button
                key={slide.id}
                onClick={() => handleProgressClick(index)}
                className="relative z-10 w-4 h-4 rounded-full border-2 border-white bg-red-500 transform -translate-y-1/2 hover:scale-110 transition-transform duration-200"
                style={{
                  left: `${(index / (slides.length - 1)) * 100}%`,
                  transform: "translateX(-50%) translateY(-50%)",
                }}
              />
            ))}
          </div>
        </div>

        {/* Progress Labels */}
        <div className="flex justify-around mt-4">
          {slides.map((slide, index) => (
            <button
              key={slide.id}
              onClick={() => handleProgressClick(index)}
              className={`text-sm font-medium transition-colors duration-300 ${
                index === currentSlide
                  ? "text-red-500"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              {slide.title}
            </button>
          ))}
        </div>
      </div>

      {/* Slider Content Section */}
      <div ref={sliderRef} className="relative overflow-hidden w-full">
        <div className="slides-wrapper flex" style={{ width: `${slides.length * 100}%` }}>
          {slides.map((slide, slideIndex) => (
            <div
              key={slide.id}
              className="w-full flex-shrink-0 px-4"
              style={{ width: `${100 / slides.length}%` }}
              ref={(el) => (slideItemsRef.current[slideIndex] = el)}
            >
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Images Section */}
                <div className="relative">
                  <div className="grid grid-cols-2 grid-rows-2 gap-2 max-h-[80%] pt-2">
                    {slide.images.map((image, index) => (
                      <div
                        key={index}
                        className={`relative overflow-hidden rounded-lg ${
                          index === 0 ? "col-span-2" : ""
                        }`}
                      >
                        <img
                          src={image}
                          alt={`${slide.title} ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                    ))}
                  </div>

                  {/* Show next slide preview (20% visible) on the right */}
                  {slideIndex === currentSlide && currentSlide < slides.length - 1 && (
                    <div className="absolute -right-16 top-0 w-16 h-full opacity-60 pointer-events-none">
                      <div className="grid grid-cols-1 gap-2 h-full">
                        <img
                          src={slides[currentSlide + 1].images[0]}
                          alt="Next slide preview"
                          className="w-full h-1/2 object-cover rounded-lg"
                        />
                        <img
                          src={slides[currentSlide + 1].images[1]}
                          alt="Next slide preview"
                          className="w-full h-1/2 object-cover rounded-lg"
                        />
                      </div>
                    </div>
                  )}

                  {/* Show previous slide preview (20% visible) on the left */}
                  {slideIndex === currentSlide && currentSlide > 0 && (
                    <div className="absolute -left-16 top-0 w-16 h-full opacity-60 pointer-events-none">
                      <div className="grid grid-cols-1 gap-2 h-full">
                        <img
                          src={slides[currentSlide - 1].images[0]}
                          alt="Previous slide preview"
                          className="w-full h-1/2 object-cover rounded-lg"
                        />
                        <img
                          src={slides[currentSlide - 1].images[1]}
                          alt="Previous slide preview"
                          className="w-full h-1/2 object-cover rounded-lg"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <div className="space-y-6">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">
                      {slide.title}
                    </h2>
                    <p className="text-gray-600 text-lg leading-relaxed mb-6">
                      {slide.description}
                    </p>
                    <p className="text-gray-500 leading-relaxed mb-8">
                      {slide.extendedDescription}
                    </p>
                  </div>

                  <button className="inline-flex items-center px-6 py-3 border-2 border-red-500 text-red-500 font-medium rounded-full hover:bg-red-500 hover:text-white transition-colors duration-300">
                    Read More
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
