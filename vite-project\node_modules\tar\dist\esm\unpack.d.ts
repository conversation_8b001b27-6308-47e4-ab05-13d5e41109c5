/// <reference types="node" resolution-mode="require"/>
import { type Stats } from 'node:fs';
import { MkdirError } from './mkdir.js';
import { Parser } from './parse.js';
import { TarOptions } from './options.js';
import { PathReservations } from './path-reservations.js';
import { ReadEntry } from './read-entry.js';
import { WarnData } from './warn-method.js';
declare const ONENTRY: unique symbol;
declare const CHECKFS: unique symbol;
declare const CHECKFS2: unique symbol;
declare const PRUNECACHE: unique symbol;
declare const ISREUSABLE: unique symbol;
declare const MAKEFS: unique symbol;
declare const FILE: unique symbol;
declare const DIRECTORY: unique symbol;
declare const LINK: unique symbol;
declare const SYMLINK: unique symbol;
declare const HARDLINK: unique symbol;
declare const UNSUPPORTED: unique symbol;
declare const CHECKPATH: unique symbol;
declare const MKDIR: unique symbol;
declare const ONERROR: unique symbol;
declare const PENDING: unique symbol;
declare const PEND: unique symbol;
declare const UNPEND: unique symbol;
declare const ENDED: unique symbol;
declare const MAYBECLOSE: unique symbol;
declare const SKIP: unique symbol;
declare const DOCHOWN: unique symbol;
declare const UID: unique symbol;
declare const GID: unique symbol;
declare const CHECKED_CWD: unique symbol;
export declare class Unpack extends Parser {
    [ENDED]: boolean;
    [CHECKED_CWD]: boolean;
    [PENDING]: number;
    reservations: PathReservations;
    transform?: TarOptions['transform'];
    writable: true;
    readable: false;
    dirCache: Exclude<TarOptions['dirCache'], undefined>;
    uid?: number;
    gid?: number;
    setOwner: boolean;
    preserveOwner: boolean;
    processGid?: number;
    processUid?: number;
    maxDepth: number;
    forceChown: boolean;
    win32: boolean;
    newer: boolean;
    keep: boolean;
    noMtime: boolean;
    preservePaths: boolean;
    unlink: boolean;
    cwd: string;
    strip: number;
    processUmask: number;
    umask: number;
    dmode: number;
    fmode: number;
    chmod: boolean;
    constructor(opt?: TarOptions);
    warn(code: string, msg: string | Error, data?: WarnData): void;
    [MAYBECLOSE](): void;
    [CHECKPATH](entry: ReadEntry): boolean;
    [ONENTRY](entry: ReadEntry): void;
    [ONERROR](er: Error, entry: ReadEntry): void;
    [MKDIR](dir: string, mode: number, cb: (er?: null | MkdirError, made?: string) => void): void;
    [DOCHOWN](entry: ReadEntry): boolean;
    [UID](entry: ReadEntry): number | undefined;
    [GID](entry: ReadEntry): number | undefined;
    [FILE](entry: ReadEntry, fullyDone: () => void): void;
    [DIRECTORY](entry: ReadEntry, fullyDone: () => void): void;
    [UNSUPPORTED](entry: ReadEntry): void;
    [SYMLINK](entry: ReadEntry, done: () => void): void;
    [HARDLINK](entry: ReadEntry, done: () => void): void;
    [PEND](): void;
    [UNPEND](): void;
    [SKIP](entry: ReadEntry): void;
    [ISREUSABLE](entry: ReadEntry, st: Stats): boolean;
    [CHECKFS](entry: ReadEntry): void;
    [PRUNECACHE](entry: ReadEntry): void;
    [CHECKFS2](entry: ReadEntry, fullyDone: (er?: Error) => void): void;
    [MAKEFS](er: null | undefined | Error, entry: ReadEntry, done: () => void): void;
    [LINK](entry: ReadEntry, linkpath: string, link: 'link' | 'symlink', done: () => void): void;
}
export declare class UnpackSync extends Unpack {
    sync: true;
    [MAKEFS](er: null | Error | undefined, entry: ReadEntry): void;
    [CHECKFS](entry: ReadEntry): void;
    [FILE](entry: ReadEntry, done: () => void): void;
    [DIRECTORY](entry: ReadEntry, done: () => void): void;
    [MKDIR](dir: string, mode: number): unknown;
    [LINK](entry: ReadEntry, linkpath: string, link: 'link' | 'symlink', done: () => void): void;
}
export {};
//# sourceMappingURL=unpack.d.ts.map